# Getting Started

## 🚀 Initial Setup

### 1. <PERSON>lone the Repository

```bash
# Clone the repository
git clone <repository-url>
cd appsolve-template

# Open project in Xcode
open Appsolve.xcodeproj
```

### 2. Project Structure

After cloning, you'll see the following structure:

```
Appsolve/
├── Application/           # App entry point and configuration
│   ├── AppDelegate.swift
│   ├── SceneDelegate.swift
│   └── GoogleService-Info.plist
├── Domain/               # Business logic and entities
│   ├── Entities/
│   ├── UseCases/
│   └── Repositories/
├── Infrastructure/       # External services and APIs
│   ├── Firebase/
│   ├── RevenueCat/
│   └── Networking/
├── Presentation/         # UI layer and view models
│   ├── Views/
│   ├── ViewModels/
│   └── Components/
├── DesignSystem/        # UI components and themes
│   ├── Components/
│   ├── Tokens/
│   └── Themes/
└── Resources/           # Assets and configuration files
    ├── Assets.xcassets
    ├── Info.plist
    └── Localizable.strings
```

### 3. Dependencies Management

The project uses Swift Package Manager for dependency management. Key dependencies include:

```swift
// Core Firebase Services
.package(url: "https://github.com/firebase/firebase-ios-sdk", from: "11.9.0")

// Networking
.package(url: "https://github.com/Moya/Moya.git", from: "15.0.3")

// Image Loading
.package(url: "https://github.com/onevcat/Kingfisher.git", from: "8.3.0")

// Subscription Management
.package(url: "https://github.com/RevenueCat/purchases-ios-spm.git", from: "5.28.1")

// Authentication
.package(url: "https://github.com/google/GoogleSignIn-iOS", from: "8.0.0")

// UI Components
.package(url: "https://github.com/airbnb/lottie-spm.git", from: "4.5.1")

// Error Reporting
.package(url: "https://github.com/getsentry/sentry-cocoa", from: "8.0.0")
```

### 4. Install Dependencies

Dependencies will be automatically resolved when you build the project:

```bash
# Dependencies are managed via Swift Package Manager
# They will be automatically resolved when building in Xcode
```

If you encounter issues, you can manually resolve them:

1. Open Xcode
2. Go to `File > Packages > Resolve Package Versions`
3. Wait for resolution to complete

### 5. Initial Build

Before configuring external services, let's make sure the project builds:

1. **Select Target**: Choose your device or simulator
2. **Build Project**: Press `Cmd+B` or use the build button
3. **Fix Any Issues**: Address any build errors that appear

### 6. Bundle Identifier

Update the bundle identifier to match your app:

1. Select the project in Xcode navigator
2. Choose your target
3. In the "General" tab, update the "Bundle Identifier"
4. Make note of this identifier - you'll need it for Firebase and other services

### 7. Development Team

Set your development team:

1. In the "Signing & Capabilities" tab
2. Select your development team from the dropdown
3. Ensure "Automatically manage signing" is checked

## 🔧 Environment Setup

### Development Environment

For development, you'll use:

- **Debug Configuration**: Enabled by default
- **Sandbox Mode**: For testing subscriptions
- **Debug Logging**: Detailed logs for troubleshooting
- **Local Development**: Using simulators and test devices

### Key Configuration Files

Before proceeding to service setup, familiarize yourself with these important files:

- `GoogleService-Info.plist`: Firebase configuration (you'll replace this)
- `Info.plist`: App metadata and permissions
- `Appsolve.entitlements`: App capabilities
- `RemoteConfigDefaults.plist`: Default remote config values

## 📝 Next Steps

Now that you have the basic project set up, you're ready to configure the external services:

1. **[Firebase Setup](/docs/firebase-setup)** - Configure authentication, database, and analytics
2. **[Authentication](/docs/authentication)** - Set up Google Sign-In
3. **[Subscriptions](/docs/subscriptions)** - Configure RevenueCat for premium features
4. **[Backend Setup](/docs/backend-setup)** - Deploy your Cloudflare Workers backend

## 🆘 Common Setup Issues

### Build Errors

If you encounter build errors:

1. **Clean Build Folder**: `Cmd+Shift+K`
2. **Reset Package Caches**: `File > Packages > Reset Package Caches`
3. **Check Xcode Version**: Ensure you're using Xcode 15.0+
4. **Verify Swift Version**: Make sure you're using Swift 5.0+

### Package Resolution Issues

If dependencies fail to resolve:

1. Check your internet connection
2. Try resolving packages manually: `File > Packages > Resolve Package Versions`
3. Clear derived data: `Xcode > Preferences > Locations > Derived Data > Delete`
4. Restart Xcode

### Signing Issues

If you encounter signing problems:

1. Ensure you have a valid Apple Developer account
2. Check that your development team is selected
3. Verify your bundle identifier is unique
4. Make sure certificates are up to date

---

**Next**: Once your project builds successfully, continue to [Firebase Setup](/docs/firebase-setup) to configure your backend services.
