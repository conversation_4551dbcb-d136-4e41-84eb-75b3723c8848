# Authentication Setup

## 🔐 Google Sign-In Configuration

This guide covers setting up Google Sign-In authentication, which works alongside Firebase Authentication to provide a seamless user experience.

## 1. Google Cloud Console Setup

### Step 1: Create or Access Project

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Select the same project you used for Firebase (or create a new one)
3. Make sure billing is enabled (required for some APIs)

### Step 2: Enable Google Sign-In API

1. Go to "APIs & Services" > "Library"
2. Search for "Google Sign-In API"
3. Click on it and press "Enable"

### Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type (unless you have Google Workspace)
3. Fill in the required information:
   - **App name**: Your app name
   - **User support email**: Your email
   - **Developer contact information**: Your email
4. Add scopes (the defaults are usually sufficient):
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
   - `openid`
5. Save and continue

### Step 4: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "iOS" as application type
4. **Name**: Give it a descriptive name
5. **Bundle ID**: Enter your app's bundle identifier (from Xcode)
6. Click "Create"

## 2. Firebase Authentication Setup

### Enable Google Sign-In in Firebase

1. Go to Firebase Console > Authentication > Sign-in method
2. Click on "Google" provider
3. Click "Enable"
4. **Project support email**: Select your email
5. Click "Save"

### Configure Web Client ID

1. In Firebase Console, go to Project Settings
2. Scroll down to "Your apps" section
3. Click on your iOS app
4. Note down the "Web client ID" - you'll need this

## 3. iOS App Configuration

### Step 1: Update GoogleService-Info.plist

Your `GoogleService-Info.plist` should already contain the necessary configuration. Verify it includes:

- `CLIENT_ID`
- `REVERSED_CLIENT_ID`
- `API_KEY`
- `GCM_SENDER_ID`
- `PROJECT_ID`

### Step 2: Configure URL Schemes

The URL scheme should already be configured in your `Info.plist`. Verify it exists:

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>REVERSED_CLIENT_ID</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>YOUR_REVERSED_CLIENT_ID_HERE</string>
        </array>
    </dict>
</array>
```

The `REVERSED_CLIENT_ID` value comes from your `GoogleService-Info.plist` file.

### Step 3: Verify App Delegate Configuration

The template already includes the necessary configuration:

```swift
import GoogleSignIn
import Firebase

@main
struct AppsolveApp: App {
    init() {
        FirebaseApp.configure()
        
        // Configure Google Sign-In
        guard let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
              let plist = NSDictionary(contentsOfFile: path),
              let clientId = plist["CLIENT_ID"] as? String else {
            fatalError("GoogleService-Info.plist not found or CLIENT_ID missing")
        }
        
        GIDSignIn.sharedInstance.configuration = GIDConfiguration(clientID: clientId)
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .onOpenURL { url in
                    GIDSignIn.sharedInstance.handle(url)
                }
        }
    }
}
```

## 4. Authentication Service Implementation

The template includes a complete authentication service. Here's how it works:

### AuthenticationService

```swift
import Firebase
import FirebaseAuth
import GoogleSignIn

class AuthenticationService: ObservableObject {
    @Published var user: User?
    @Published var isAuthenticated = false
    
    init() {
        // Listen for authentication state changes
        Auth.auth().addStateDidChangeListener { [weak self] _, user in
            self?.user = user
            self?.isAuthenticated = user != nil
        }
    }
    
    // Google Sign-In
    func signInWithGoogle() async throws {
        guard let presentingViewController = await UIApplication.shared.windows.first?.rootViewController else {
            throw AuthenticationError.noViewController
        }
        
        let result = try await GIDSignIn.sharedInstance.signIn(withPresenting: presentingViewController)
        
        guard let idToken = result.user.idToken?.tokenString else {
            throw AuthenticationError.noIDToken
        }
        
        let credential = GoogleAuthProvider.credential(
            withIDToken: idToken,
            accessToken: result.user.accessToken.tokenString
        )
        
        try await Auth.auth().signIn(with: credential)
    }
    
    // Email Sign-In
    func signInWithEmail(email: String, password: String) async throws {
        try await Auth.auth().signIn(withEmail: email, password: password)
    }
    
    // Email Sign-Up
    func signUpWithEmail(email: String, password: String) async throws {
        try await Auth.auth().createUser(withEmail: email, password: password)
    }
    
    // Sign Out
    func signOut() throws {
        try Auth.auth().signOut()
        GIDSignIn.sharedInstance.signOut()
    }
}
```

## 5. UI Implementation

### Login View

The template includes a complete login interface:

```swift
struct LoginView: View {
    @StateObject private var authService = AuthenticationService()
    @State private var email = ""
    @State private var password = ""
    @State private var isLoading = false
    
    var body: some View {
        VStack(spacing: 20) {
            // App Logo
            Image("app_logo")
                .resizable()
                .scaledToFit()
                .frame(height: 100)
            
            // Email/Password Fields
            VStack(spacing: 16) {
                TextField("Email", text: $email)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
                
                SecureField("Password", text: $password)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            // Sign In Button
            Button("Sign In") {
                Task {
                    isLoading = true
                    try await authService.signInWithEmail(email: email, password: password)
                    isLoading = false
                }
            }
            .disabled(isLoading)
            
            // Google Sign-In Button
            Button("Sign in with Google") {
                Task {
                    isLoading = true
                    try await authService.signInWithGoogle()
                    isLoading = false
                }
            }
            .disabled(isLoading)
        }
        .padding()
    }
}
```

## 6. Testing Authentication

### Test Google Sign-In

1. Build and run your app
2. Tap "Sign in with Google"
3. Complete the Google sign-in flow
4. Verify user is authenticated in Firebase Console

### Test Email Authentication

1. Create a test user in Firebase Console:
   - Go to Authentication > Users
   - Click "Add user"
   - Enter email and password
2. Test sign-in with these credentials

### Debug Authentication Issues

Enable authentication debugging:

```swift
#if DEBUG
// Add to your app initialization
Auth.auth().settings?.isAppVerificationDisabledForTesting = true
#endif
```

## 7. Security Considerations

### Production Setup

For production apps:

1. **OAuth Consent Screen**: Submit for verification if needed
2. **API Restrictions**: Restrict your API keys in Google Cloud Console
3. **Bundle ID Verification**: Ensure bundle IDs match exactly
4. **Certificate Pinning**: Consider implementing for additional security

### User Data Protection

The template includes proper user data handling:

- User data is stored securely in Firebase
- Authentication tokens are managed automatically
- User sessions persist across app launches
- Proper sign-out clears all local data

## 🆘 Troubleshooting

### Common Issues

**Google Sign-In Not Working**:
- Verify bundle ID matches in all configurations
- Check URL schemes in Info.plist
- Ensure OAuth consent screen is configured

**Firebase Authentication Errors**:
- Check Firebase project configuration
- Verify GoogleService-Info.plist is correct
- Ensure authentication providers are enabled

**Build Errors**:
- Clean build folder and rebuild
- Check for conflicting dependencies
- Verify all required frameworks are linked

## 📝 Next Steps

With authentication working, you can now set up subscription management:

**[Subscriptions Setup](/docs/subscriptions)** - Configure RevenueCat for premium features

---

**Note**: Always test authentication on both simulator and physical devices, as some features may behave differently.
