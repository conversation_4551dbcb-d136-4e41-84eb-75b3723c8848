# Appsolve iOS App - Quick Start Guide

## 📋 Overview

Appsolve is a SwiftUI-based iOS application template that enables developers to quickly build and deploy AI-powered mobile applications. The template features a clean architecture with modular design, Firebase integration, subscription management through RevenueCat, and enterprise-grade Cloudflare Workers backend with Hono framework.

## 🎯 Key Features

- **AI-Powered Functionality**: Ready-to-use AI integration templates
- **Modern SwiftUI Architecture**: Clean, maintainable code structure
- **Firebase Full Suite**: Authentication, Firestore, Storage, Analytics
- **Subscription System**: Premium features with RevenueCat integration
- **Enterprise Backend**: Cloudflare Workers with Hono framework
- **Social Authentication**: Google Sign-In and email authentication
- **Real-time Sync**: Firebase Firestore for data synchronization

## 🏗️ Architecture Overview

The app follows Clean Architecture principles with clear separation of concerns:

```
Appsolve/
├── Application/           # App entry point, delegates, modules
├── Domain/               # Business logic, entities, use cases
├── Infrastructure/       # External services, databases, APIs
├── Presentation/         # UI layer, views, view models
├── DesignSystem/        # UI components, tokens, themes
└── Documentation/       # Technical documentation
```

### Core Modules

- **Auth Module**: User authentication and session management
- **Core Module**: Main application functionality and workflows
- **Subscription Module**: Premium features and payment processing
- **Media Module**: Asset storage and management

## 🚀 Getting Started

### Prerequisites

- **Xcode 15.0+**
- **iOS 16.0+** deployment target
- **Swift 5.0+**
- **Firebase Project** (configured)
- **RevenueCat Account** (for subscriptions)
- **Google Cloud Project** (for authentication)
- **Cloudflare Account** (for backend services)

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd appsolve-template

# Open project in Xcode
open Appsolve.xcodeproj
```

### 2. Firebase Configuration

1. **Create Firebase Project**:
   - Go to [Firebase Console](https://console.firebase.google.com)
   - Create new project or use existing one
   - Enable Authentication, Firestore, Storage, Analytics

2. **Download Configuration**:
   - Download `GoogleService-Info.plist`
   - Replace existing file in `Appsolve/Application/`

3. **Configure Authentication**:
   - Enable Email/Password authentication
   - Enable Google Sign-In
   - Add iOS app with your bundle ID

4. **Setup Firestore Database**:
   - Create Firestore database in production mode
   - Configure security rules for your use case

### 3. Google Sign-In Setup

1. **Configure OAuth**:
   - Go to Google Cloud Console
   - Enable Google Sign-In API
   - Configure OAuth consent screen
   - Add authorized domains

2. **Update URL Schemes**:
   - Verify `Info.plist` contains correct URL scheme
   - Should match `REVERSED_CLIENT_ID` from GoogleService-Info.plist

### 4. RevenueCat Configuration

1. **Create RevenueCat Project**:
   - Sign up at [RevenueCat](https://www.revenuecat.com)
   - Create new project
   - Configure iOS app

2. **Setup Products**:
   - Create subscription products in App Store Connect
   - Configure products in RevenueCat dashboard
   - Set up entitlements (pro_tier with features)

3. **Update API Key**:
   - Get API key from RevenueCat dashboard
   - Configure in Firebase Remote Config or hardcode for testing

### 5. Cloudflare Workers Backend Setup

1. **Create Cloudflare Account**:
   - Sign up at [Cloudflare](https://cloudflare.com)
   - Set up Workers & Pages

2. **Deploy Hono Backend**:
   - Use the included Hono framework template
   - Configure API endpoints for your app needs
   - Set up environment variables and secrets

3. **Configure API Integration**:
   - Update base URL in `APIEndpoint.swift`
   - Set up authentication tokens via `AccessTokenPlugin`
   - Configure request/response interceptors

### 6. Third-Party Services

The app integrates several third-party services:

- **Firebase**: Authentication, Firestore, Storage, Analytics
- **RevenueCat**: Subscription management
- **Cloudflare Workers**: Enterprise-grade backend with Hono
- **Sentry**: Error reporting and monitoring
- **Moya**: Network layer abstraction
- **Kingfisher**: Image loading and caching
- **Lottie**: Animation support

### 7. Build and Run

1. **Install Dependencies**:
   ```bash
   # Dependencies are managed via Swift Package Manager
   # They will be automatically resolved when building
   ```

2. **Configure Build Settings**:
   - Set development team in project settings
   - Verify bundle identifier matches Firebase configuration
   - Ensure all required capabilities are enabled

3. **Build Project**:
   ```bash
   # Build from Xcode or command line
   xcodebuild -project Appsolve.xcodeproj -scheme Appsolve -configuration Debug
   ```

## 🔧 Configuration

### Environment Setup

1. **Development Environment**:
   - Use Debug configuration
   - Firebase debug logging enabled
   - Sentry debug mode
   - RevenueCat sandbox mode

2. **Production Environment**:
   - Use Release configuration
   - Disable debug logging
   - Production Firebase project
   - RevenueCat production mode

### Key Configuration Files

- `GoogleService-Info.plist`: Firebase configuration
- `Info.plist`: App metadata and permissions
- `Appsolve.entitlements`: App capabilities
- `RemoteConfigDefaults.plist`: Default remote config values

### Dependencies Management

The project uses Swift Package Manager for dependency management. Key dependencies include:

```swift
// Core Firebase Services
.package(url: "https://github.com/firebase/firebase-ios-sdk", from: "11.9.0")

// Networking
.package(url: "https://github.com/Moya/Moya.git", from: "15.0.3")

// Image Loading
.package(url: "https://github.com/onevcat/Kingfisher.git", from: "8.3.0")

// Subscription Management
.package(url: "https://github.com/RevenueCat/purchases-ios-spm.git", from: "5.28.1")

// Authentication
.package(url: "https://github.com/google/GoogleSignIn-iOS", from: "8.0.0")

// UI Components
.package(url: "https://github.com/airbnb/lottie-spm.git", from: "4.5.1")

// Error Reporting
.package(url: "https://github.com/getsentry/sentry-cocoa", from: "8.0.0")
```

## 📱 App Structure

### Main Navigation

The app uses a custom tab-based navigation with main sections that you can customize:

1. **Main Page**: Core application functionality
2. **Profile Page**: User profile management and settings
3. **Additional Pages**: Customize based on your app needs

### Authentication Flow

- **Login View**: Email and Google Sign-In options
- **Root View**: Handles authentication state transitions
- **User State Management**: Centralized user session handling

### Core Workflows

1. **User Onboarding**:
   - User registration and authentication
   - Profile setup and preferences
   - Feature introduction and tutorials

2. **Subscription Management**:
   - Paywall presentation
   - Purchase processing
   - Entitlement verification

## 🧪 Testing

### Running Tests

```bash
# Run unit tests
xcodebuild test -project Appsolve.xcodeproj -scheme Appsolve -destination 'platform=iOS Simulator,name=iPhone 15'

# Run specific test target
xcodebuild test -project Appsolve.xcodeproj -scheme Appsolve -only-testing:AppsolveTests
```

### Test Structure

- Unit tests for business logic and use cases
- Integration tests for Firebase services
- UI tests for critical user flows
- Mock implementations for external dependencies

## 🚀 Deployment

### App Store Preparation

1. **Configure Release Build**:
   - Set Release configuration
   - Update version and build numbers
   - Verify all certificates and provisioning profiles

2. **Archive and Upload**:
   ```bash
   # Archive for distribution
   xcodebuild archive -project Appsolve.xcodeproj -scheme Appsolve -archivePath Appsolve.xcarchive
   ```

### Production Checklist

- [ ] Firebase production project configured
- [ ] RevenueCat production setup complete
- [ ] Cloudflare Workers backend deployed
- [ ] All API keys and secrets secured
- [ ] App Store metadata and screenshots ready
- [ ] Privacy policy and terms of service updated
- [ ] Beta testing completed via TestFlight

## 🆘 Troubleshooting

### Common Issues

1. **Build Errors**:
   - Clean build folder (`Cmd+Shift+K`)
   - Reset package caches (`File > Packages > Reset Package Caches`)
   - Verify all dependencies are resolved

2. **Firebase Connection Issues**:
   - Verify GoogleService-Info.plist is correctly placed
   - Check bundle identifier matches Firebase configuration
   - Ensure Firebase services are enabled in console

3. **Authentication Problems**:
   - Verify Google Sign-In configuration
   - Check URL schemes in Info.plist
   - Ensure OAuth consent screen is configured

4. **Subscription Issues**:
   - Verify RevenueCat API key is correct
   - Check App Store Connect product configuration
   - Ensure sandbox testing is properly set up

### Getting Help

- Check existing documentation in `/Documentation/`
- Review Firebase and RevenueCat console logs
- Use Xcode debugger and console for runtime issues
- Consult third-party service documentation

---

**Note**: This guide provides the essential steps to get started with Appsolve. Customize the template according to your specific app requirements and business logic.
