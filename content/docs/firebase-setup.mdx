# Firebase Setup

## 🔥 Firebase Configuration

Firebase provides the backend infrastructure for authentication, database, storage, and analytics. This guide will walk you through setting up all the necessary Firebase services.

## 1. Create Firebase Project

### Step 1: Go to Firebase Console

1. Visit [Firebase Console](https://console.firebase.google.com)
2. Sign in with your Google account
3. Click "Create a project" or "Add project"

### Step 2: Configure Project

1. **Project Name**: Enter your app name (e.g., "My Appsolve App")
2. **Project ID**: Firebase will generate a unique ID (note this down)
3. **Analytics**: Choose whether to enable Google Analytics (recommended)
4. **Analytics Account**: Select or create a Google Analytics account
5. Click "Create project"

## 2. Add iOS App to Firebase

### Step 1: Register App

1. In your Firebase project dashboard, click the iOS icon
2. **Bundle ID**: Enter the bundle identifier from your Xcode project
3. **App Nickname**: Give your app a recognizable name
4. **App Store ID**: Leave blank for now (add later when published)
5. Click "Register app"

### Step 2: Download Configuration File

1. Download the `GoogleService-Info.plist` file
2. **Important**: Replace the existing file in your Xcode project
3. Location: `Appsolve/Application/GoogleService-Info.plist`
4. Make sure it's added to your target

### Step 3: Verify Installation

The Firebase SDK is already included in the template. Verify it's working:

```swift
// This code is already in your AppDelegate
import Firebase

@main
struct AppsolveApp: App {
    init() {
        FirebaseApp.configure()
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}
```

## 3. Enable Firebase Services

### Authentication

1. In Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable the following providers:
   - **Email/Password**: Click and enable
   - **Google**: Click, enable, and configure (see next section)

### Firestore Database

1. Go to "Firestore Database"
2. Click "Create database"
3. **Security Rules**: Start in production mode (we'll configure rules later)
4. **Location**: Choose a location close to your users
5. Click "Done"

### Storage

1. Go to "Storage"
2. Click "Get started"
3. **Security Rules**: Start in production mode
4. **Location**: Use the same location as Firestore
5. Click "Done"

### Analytics

If you enabled Analytics during project creation:

1. Go to "Analytics"
2. Review the default settings
3. Analytics will automatically start collecting data

## 4. Configure Security Rules

### Firestore Rules

Replace the default Firestore rules with these basic authenticated user rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Add more rules based on your app's data structure
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### Storage Rules

Update Storage rules for authenticated users:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 5. Remote Config Setup

Remote Config allows you to change app behavior without updating the app:

### Enable Remote Config

1. Go to "Remote Config" in Firebase Console
2. Click "Create configuration"
3. Add these default parameters:

```json
{
  "revenuecat_api_key": "your_revenuecat_api_key_here",
  "enable_premium_features": true,
  "max_free_usage": 5,
  "paywall_display_delay": 2.0,
  "app_version_required": "1.0.0"
}
```

### Configure in App

The template includes Remote Config setup:

```swift
// Already configured in your app
import FirebaseRemoteConfig

class RemoteConfigService {
    private let remoteConfig = RemoteConfig.remoteConfig()
    
    func configure() {
        let settings = RemoteConfigSettings()
        settings.minimumFetchInterval = 0 // For development
        remoteConfig.configSettings = settings
        
        // Set default values
        remoteConfig.setDefaults(fromPlist: "RemoteConfigDefaults")
    }
    
    func fetchAndActivate() async {
        do {
            let status = try await remoteConfig.fetchAndActivate()
            print("Remote config fetched: \(status)")
        } catch {
            print("Error fetching remote config: \(error)")
        }
    }
}
```

## 6. Test Firebase Connection

### Build and Test

1. Build your project (`Cmd+B`)
2. Run on simulator or device (`Cmd+R`)
3. Check Xcode console for Firebase initialization logs

### Verify Services

You should see logs similar to:

```
[Firebase/Core][I-COR000003] The default Firebase app has not yet been configured.
[Firebase/Core][I-COR000001] Configuring the default app.
[Firebase/Analytics][I-ACS023007] Analytics v.X.X.X started
[Firebase/Analytics][I-ACS023008] To enable debug logging set the following application argument: -FIRAnalyticsDebugEnabled
```

## 7. Environment Configuration

### Development vs Production

For development, you can enable debug logging:

```swift
#if DEBUG
// Enable Firebase debug logging
FirebaseConfiguration.shared.setLoggerLevel(.debug)
#endif
```

### Multiple Environments

If you need separate Firebase projects for development and production:

1. Create separate Firebase projects
2. Use different `GoogleService-Info.plist` files
3. Configure build schemes to use appropriate files

## 🆘 Troubleshooting

### Common Issues

**Bundle ID Mismatch**:
- Ensure bundle ID in Xcode matches Firebase configuration
- Check `GoogleService-Info.plist` has correct bundle ID

**Configuration File Issues**:
- Make sure `GoogleService-Info.plist` is added to your target
- Verify file is in the correct location: `Appsolve/Application/`

**Build Errors**:
- Clean build folder (`Cmd+Shift+K`)
- Ensure Firebase SDK version is compatible
- Check for conflicting dependencies

### Debug Tips

Enable detailed Firebase logging:

```swift
// Add to your app initialization
#if DEBUG
FirebaseApp.configure()
FirebaseConfiguration.shared.setLoggerLevel(.debug)
#endif
```

## 📝 Next Steps

With Firebase configured, you're ready to set up authentication:

**[Authentication Setup](/docs/authentication)** - Configure Google Sign-In and email authentication

---

**Note**: Keep your `GoogleService-Info.plist` file secure and never commit it to public repositories with real production keys.
