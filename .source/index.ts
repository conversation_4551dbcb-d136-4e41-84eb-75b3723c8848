// @ts-nocheck -- skip type checking
import * as docs_0 from "../content/docs/quickstart.mdx?collection=docs&hash=1752567730927"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"quickstart.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/quickstart.mdx"}, data: docs_0 }], [{"info":{"path":"meta.json","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/meta.json"},"data":{"pages":["quickstart"]}}, {"info":{"path":"meta.zh.json","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/meta.zh.json"},"data":{"pages":["quickstart"]}}])